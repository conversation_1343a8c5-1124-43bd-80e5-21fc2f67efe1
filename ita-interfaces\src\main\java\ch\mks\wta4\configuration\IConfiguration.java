package ch.mks.wta4.configuration;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import ch.mks.wta4.common.config.WTAEnvironmentConfiguration;
import ch.mks.wta4.common.constants.ReferenceKey;
import ch.mks.wta4.common.constants.ReferenceType;
import ch.mks.wta4.configuration.model.AuctionCommissionOverride;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration;
import ch.mks.wta4.configuration.model.BookingAggregationInstruction;
import ch.mks.wta4.configuration.model.BusinessUnit;
import ch.mks.wta4.configuration.model.Category;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.Currency;
import ch.mks.wta4.configuration.model.CurrencyPair;
import ch.mks.wta4.configuration.model.DefaultFixConfiguration;
import ch.mks.wta4.configuration.model.Device;
import ch.mks.wta4.configuration.model.FixSessionConfiguration;
import ch.mks.wta4.configuration.model.HedgeProfile;
import ch.mks.wta4.configuration.model.IHolidayCalendar;
import ch.mks.wta4.configuration.model.Location;
import ch.mks.wta4.configuration.model.MessageType;
import ch.mks.wta4.configuration.model.OfflinePriceConfiguration;
import ch.mks.wta4.configuration.model.PipConfiguration;
import ch.mks.wta4.configuration.model.PriceVariationThresholdOverride;
import ch.mks.wta4.configuration.model.Product;
import ch.mks.wta4.configuration.model.RFQConfiguration;
import ch.mks.wta4.configuration.model.Reference;
import ch.mks.wta4.configuration.model.Spread;
import ch.mks.wta4.configuration.model.StaticPrice;
import ch.mks.wta4.configuration.model.StaticPrice.StaticPriceType;
import ch.mks.wta4.configuration.model.User;
import ch.mks.wta4.configuration.model.UserAgreement;
import ch.mks.wta4.configuration.model.UserPreferenceType;
import ch.mks.wta4.configuration.model.wta.LiquidityProvider;
import ch.mks.wta4.dealercontrol.model.HedgingConfiguration;
import ch.mks.wta4.event.InstanceInfo;
import ch.mks.wta4.event.PrimaryStatus;
import ch.mks.wta4.ita.model.CacheRefreshInstruction;
import ch.mks.wta4.ita.model.ForwardCurve;
import ch.mks.wta4.ita.model.Operation;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.ita.model.SpecificTime;
import ch.mks.wta4.ita.model.TimeWithZoneId;
import ch.mks.wta4.ita.model.marketStatus.MarketStatusSchedule;
import ch.mks.wta4.priceprovider.IMarketDataSnapshotProvider;

/**
 * Core interface to access the wta4 DB configuration
 * <AUTHOR>
 *
 */
public interface IConfiguration {
	public User getUser(String userId);
	public BusinessUnit getBusinessUnit(String buId);
    public List<Double> getBands(String currencyPairId);
    public HedgeProfile getHedgeProfile(String currencyPairId, OrderType orderType);
    public Product getHedingProduct(String currencyPair);
    public BusinessUnit getRootBusinessUnit();
    public User getSystemUser();
    public long getOrderMonitorGTDPollTimeMillis();
    public Spread getSpread(String categoryId,String currencyPairId);
    public Double getMinimumSpread(String currencyPairId);
    public CurrencyPair getCurrencyPair(String currencyPairId);
    public Product getProduct(String productId);
    public List<LiquidityProvider> getLiquidityProviders();
    public int getLogonWaitTimeInSeconds();
    public BusinessUnit getLPITABusinessUnit();
    public TimeWithZoneId getEODHedgingClosingTime(String currencyPairId);
    public PipConfiguration getOverriddenPipConfiguration(String buId, String currencyPairId);
    public Double getOverriddenPriceVariationThreshold(String buId, String currencyPairId);
    public List<CurrencyPair> getAllCurrencyPairs();
    public Double getOrderPriceCheckMargin(String currencyPairId, OrderType type);
    public int getHedgingCloserMaxRetries();
    public int getHedgingCloserRetryPeriodInSeconds();
    public long getOrderMinimumTimeSeparationInMillis();
    public MarketStatusSchedule getMarketStatusSchedule();
    public List<BusinessUnit> getAllBusinessUnits();
    public List<BusinessUnit> getActiveBusinessUnits();
    public List<User> getUsersByBusinessUnit(String buId);
    public Double getAuctionCommission(String buId, String currencyPairId, Operation mksOperation);
    public Long getAuctionMinimumMinutesToSessionTime(String currencyPairId);
    public List<LocalDate> getUKAuctionHolidays(String currencyId, SpecificTime specificTime);
    public TimeWithZoneId getEODTime();
    public List<User> getMessageRecipientList(String buId, MessageType messageType);
    public ZoneId getApplicationZoneId();
    /* Referenced to the ZoneId provided by getApplicationZoneId()*/
    public String getActiveOrderListCronExpression();
    /*Referenced to the ZoneId provided by getApplicationZoneId()*/
    public String getEODRecapCronExpression();
    public long getOrderStaleCheckPeriodInSeconds();
    public long getMaxStalledTimeInSeconds();
    public String getSequentialIDGeneratorStoreFilePath();
    public String getApplicationId();
    public Reference getReferenceByTypeAndKey(ReferenceType type,ReferenceKey key);
    public Reference getReferenceByTypeAndValue(ReferenceType type, String value);
    public List<Reference> getReferenceListByType(ReferenceType type);
    public List<BusinessUnit> getBusinessUnitsByTradingCategory(String categoryId);
    public String getProjectBuildType();
    public List<HedgeProfile> getHedgingProfileByOrderType(OrderType orderType) ;
    public List<BusinessUnit> getBusinessUnitByUser(Integer userId);
    public Set<Currency> getAllCurrency();
    public Currency getCurrency(String currencyId);
    public UserAgreement getActiveUserAgreement() ;
    public BUDistributionConfiguration getBUDistributionConfiguration(String buId, Channel channel);
    public int getMaxFailedLoginAttempts();
    public List<OrderType> getAllowedOrderTypes(String buId, Channel channel);
    public boolean isEODClosingActivated();
    public List<BusinessUnit> getLPBusinessUnits();
    public String getMDSRepositoryStoreFile();
    public String getDealPendingBookingAlertCronExpression();
    public boolean isUiDisabledForCustomer();
    public boolean isDisplayHedgeBookingId();
    public String getBuIdByPK(Integer buId);
    public String getUserIdByPK(Integer userId);
    public Set<CacheRefreshInstruction> getAllCacheRefreshInstruction(Date datetime);
    public Integer getBUPKByUserIdByPK(Integer userId);
    public List<User> getMinimalUsersByBusinessUnit(String buId);
    public long getZombieOrderCheckPeriodMillis();
    public long getZombieOrderThresholdMillis();
    public void setPriceProvider(IMarketDataSnapshotProvider priceProvider);
    public List<String> getSpreadDefinedCurrencyPair();
    public boolean isFindurBookingDisabled();
    public RFQConfiguration getRFQConfiguration(String buId, String currencyPairId);
    public OfflinePriceConfiguration getOfflinePriceConfiguration(String currencyPairId);
    public List<FixSessionConfiguration> getActiveFixSessions();
    public DefaultFixConfiguration getDefaultFixConfiguration();
    public BusinessUnit getHedgingBusinessUnit();
    public List<Device> getUserDevices(String userId,String appId);
    public default Long getPriceEngineMaxInputUpdatesPerSecondPerCP() { return 100l; };
    public default String getCustomerAppId(){ return "WTA4-FWK-DEFAULT-ID"; };
    public default Long getPriceEngineOfflineStreamPeriodMillis() { return 10000l; }
    public default boolean isPushNotificationEnabled() { return true; };
    
    public String getAMQBrokerURL();
    public List<Product> getAllProducts();
    public double getMaximumDeviationOnOfflineUpdate();
    public List<OfflinePriceConfiguration> getAllOfflinePriceConfiguration();
    public String getMarketStatusTransitionNotificationList();
    public boolean getIsMarketStatusTransitionEnabled();
    
    public Set<String> getOfflineBaseCurrencyPairs();
    public Set<String> getOfflineDerivedCurrencyPairs();
    public Map<String, HedgingConfiguration> getFOKHedgingConfigurationsByCurrencyPair();
    public List<HedgeProfile> getAllHedgingProfiles();
	
    public List<Category> getAllTradingCategories();
    public List<AuctionCommissionOverride> getAllAuctionCommissionOverrides();
    
    public StaticPrice getStaticPrice(String currencyPairId, StaticPriceType type);
    public List<StaticPrice> getAllStaticPricesOfType(StaticPriceType type);
    public default Long getPriceEngineOnlineStaticPriceStreamPeriodMillis() { return 10000l; }
    public default String getCurrencyPairIdBySymbolAndLocation(String symbol, Location location) { return symbol; };
    public default List<BookingAggregationInstruction> getBookingAggregationInstructions(){ return new ArrayList<>();};
    public List<String> getOfflineTradingAllowedBUList();
    public default long getAggregatedBookingEngineCheckPeriodSeconds() { return 60l;};
    
    public default LocalTime getAggregatedBookingEngineClosingTime() {return LocalTime.parse("16:55:00.000");}; // ensure the time plus the buffer crosses the closing time
    public default ZoneId getAggregatedBookingEngineClosingTimeZoneId() {return ZoneId.of("America/New_York");};
    public default int getAggregatedBookingEngineClosingTimeBufferSeconds() {return 6*60;};
    public String getUserPreferences(String appId,String user,Channel channel,UserPreferenceType  userPreferenceType);
    public String getDeviceCredentials(String appId, String deviceId);
    public boolean shouldWeDropCrossedPrices();
    public Double getCrossedPriceThresholdPct();
    public List<String> getStaleQuoteExcludedBUList();
    public List<String> getPVTExcludedBUList();
    public List<String> getBUListForEODCustomerDealRecap();
    public boolean isFindurOZBookingEnabled();
    public default List<BUDistributionConfiguration> getAllBUDistributionConfigurations(){ return new ArrayList<>();};
    public List<PriceVariationThresholdOverride> getAllPriceVariationThresholdOverrides();
    public default long getFindurPositionWTAOrderCacheTimeToLiveMillis() { return 60000l; };
    public default long getFindurPositionRefreshPeriodSeconds() { return 60*60; }; // 1 hour
    public default long getExposureEnginePriceRefreshPeriodInSeconds() { return 60l; }; // 1 minute
    public default boolean isExposureFeatureEnabled() {return false;};
    public CurrencyPair getBusinessUnitCurrencyPair(String buId,String currencyPairId);
    public String getCustomerEODRecapCronExpression();
    public ZoneId getCustomerEODRecapCronTimeZoneId();
	long getPersistenceQueueRedeliveryInitialRedeliveryDelay();
	boolean isPersistenceQueueRedeliveryUseExponentialBackoff();
	double getPersistenceQueueRedeliveryBackOffMultiplier();
	int getPersistenceQueueRedeliveryMaximumRedeliveries();
	long getPersistenceQueueRedeliveryRedeliveryDelay();
	public String getBuCurrencyPairIdByPK(Integer buCPId);
    public default InstanceInfo getInstanceInfo() {return new InstanceInfo(WTAEnvironmentConfiguration.getAppId(), WTAEnvironmentConfiguration.getRegionId(), WTAEnvironmentConfiguration.getInstanceId());}
    public default String getEventBusURL() {return "undefined";};
    public default String getEventBusTopicName() {return "undefined";};
    public default Boolean getEventBusEnabled() {return false;}
    public default int getInstanceHeartbeatPeriodInSeconds() {return 10;}
    public PrimaryStatus getPrimaryStatus();
	public default int getRequestForHearbeatsWaitSeconds() { return 3;}
	public default int getHeartbeatsTimeToLiveInCacheInSeconds() {return this.getInstanceHeartbeatPeriodInSeconds() + 1;}
    public String getEventBusBookingQueueName();
    public String getEventBusAutoHedgerQueueName();
    public default String getEventBusFindurEventsTopicName() {return "findur-events";}
    public IHolidayCalendar getHolidayCalendar();
    public List<ForwardCurve> getAllForwardCurves();
    public ForwardCurve getForwardCurve(String currencyPairId);
}
