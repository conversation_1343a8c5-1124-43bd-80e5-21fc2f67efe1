package ch.mks.wta4.services.configuration;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import ch.mks.wta4.common.constants.ReferenceKey;
import ch.mks.wta4.common.constants.ReferenceType;
import ch.mks.wta4.common.constants.WTA4Constants;
import ch.mks.wta4.common.service.AbstractWTA4Service;
import ch.mks.wta4.common.time.TimeUtils;
import ch.mks.wta4.configuration.ICachedConfiguration;
import ch.mks.wta4.configuration.model.AuctionCommissionOverride;
import ch.mks.wta4.configuration.model.BUDistributionConfiguration;
import ch.mks.wta4.configuration.model.BookingAggregationInstruction;
import ch.mks.wta4.configuration.model.BusinessUnit;
import ch.mks.wta4.configuration.model.Category;
import ch.mks.wta4.configuration.model.Channel;
import ch.mks.wta4.configuration.model.Currency;
import ch.mks.wta4.configuration.model.CurrencyPair;
import ch.mks.wta4.configuration.model.DefaultFixConfiguration;
import ch.mks.wta4.configuration.model.Device;
import ch.mks.wta4.configuration.model.FixSessionConfiguration;
import ch.mks.wta4.configuration.model.HedgeProfile;
import ch.mks.wta4.configuration.model.IHolidayCalendar;
import ch.mks.wta4.configuration.model.Location;
import ch.mks.wta4.configuration.model.MessageType;
import ch.mks.wta4.configuration.model.OfflinePriceConfiguration;
import ch.mks.wta4.configuration.model.PipConfiguration;
import ch.mks.wta4.configuration.model.PriceVariationThresholdOverride;
import ch.mks.wta4.configuration.model.Product;
import ch.mks.wta4.configuration.model.RFQConfiguration;
import ch.mks.wta4.configuration.model.Reference;
import ch.mks.wta4.configuration.model.Spread;
import ch.mks.wta4.configuration.model.StaticPrice;
import ch.mks.wta4.configuration.model.StaticPrice.StaticPriceType;
import ch.mks.wta4.configuration.model.User;
import ch.mks.wta4.configuration.model.UserAgreement;
import ch.mks.wta4.configuration.model.UserPreferenceType;
import ch.mks.wta4.configuration.model.wta.LiquidityProvider;
import ch.mks.wta4.dealercontrol.model.HedgingConfiguration;
import ch.mks.wta4.event.PrimaryStatus;
import ch.mks.wta4.ita.model.CacheRefreshInstruction;
import ch.mks.wta4.ita.model.ForwardCurve;
import ch.mks.wta4.ita.model.Operation;
import ch.mks.wta4.ita.model.OrderType;
import ch.mks.wta4.ita.model.SpecificTime;
import ch.mks.wta4.ita.model.TimeWithZoneId;
import ch.mks.wta4.ita.model.marketStatus.MarketStatusSchedule;
import ch.mks.wta4.ita.model.marketStatus.MarketStatusScheduleFactory;
import ch.mks.wta4.priceprovider.IMarketDataSnapshotProvider;
import ch.mks.wta4.services.configuration.spreadresolver.SyntheticSpreadResolver;
import ch.mks.wta4.services.dao.service.BUDistributionConfigurationService;
import ch.mks.wta4.services.dao.service.BookingAggregationInstructionService;
import ch.mks.wta4.services.dao.service.BusinessUnitCurrencyPairService;
import ch.mks.wta4.services.dao.service.BusinessUnitService;
import ch.mks.wta4.services.dao.service.CacheRefreshInstructionService;
import ch.mks.wta4.services.dao.service.CurrencyHolidayCalendarService;
import ch.mks.wta4.services.dao.service.CurrencyPairEODService;
import ch.mks.wta4.services.dao.service.CurrencyPairService;
import ch.mks.wta4.services.dao.service.CurrencyService;
import ch.mks.wta4.services.dao.service.DeviceCredentialsService;
import ch.mks.wta4.services.dao.service.DeviceService;
import ch.mks.wta4.services.dao.service.FixSessionService;
import ch.mks.wta4.services.dao.service.ForwardCurveService;
import ch.mks.wta4.services.dao.service.HedgeProfileService;
import ch.mks.wta4.services.dao.service.LPBandService;
import ch.mks.wta4.services.dao.service.LiquidityProviderService;
import ch.mks.wta4.services.dao.service.MessageRecipientService;
import ch.mks.wta4.services.dao.service.OfflinePriceConfigurationService;
import ch.mks.wta4.services.dao.service.ProductService;
import ch.mks.wta4.services.dao.service.RFQConfigurationService;
import ch.mks.wta4.services.dao.service.ReferenceService;
import ch.mks.wta4.services.dao.service.RootBusinessUnitService;
import ch.mks.wta4.services.dao.service.StaticPriceService;
import ch.mks.wta4.services.dao.service.TradingCategoryCurrencyPairSpreadService;
import ch.mks.wta4.services.dao.service.TradingCategoryService;
import ch.mks.wta4.services.dao.service.UserAgreementService;
import ch.mks.wta4.services.dao.service.UserPreferenceService;
import ch.mks.wta4.services.dao.service.UserService;
import ch.mks.wta4.um.calendar.MKSCustomHolidayCalendar;

@Component
@Qualifier("configurationCacheService")
public class ConfigurationService extends AbstractWTA4Service implements ICachedConfiguration{

    static final Logger LOG = LoggerFactory.getLogger(ConfigurationService.class);
	private static final long AUCTION_DEFAULT_MINIMUM_MINUTES_TO_SESSION_TIME = 10l;

    @Value("${sequentialIDGenerator.storeFile:var/seq/sequence.properties}")
	private String sequentialIDGeneratorStoreFile;

    @Value("${mdsRegister.storeFile:var/mds/mds.ser}")
    private String mdsRegisterStoreFile;

    @Value("${applicationId:wta4-fwk}")
	private String applicationId;

    @Value("${project.build.type:}")
    private String projectBuildType;

    @Value("${dealTicket.api.allowed.bu.list:}")
    private List<String> dealTicketAPIAllowedBUs;

    @Value("${marketOrder.api.allowed.bu.list:}")
    private List<String> marketOrderAPIAllowedBUs;

    @Value("${offlinetrading.allowed.bu.list:}")
    private List<String> offlineTradingAllowedBUs;

    @Value("${stalecheck.exclude.bu.list:}")
    private List<String> staleCheckExcludedBUs;

    @Value("${pvtcheck.exclude.bu.list:}")
    private List<String> pvtCheckExcludedBUs;

    @Value("${b2bstrategy.eodClosingActivated:false}")
    private boolean isEODClosingActivated;

    @Value("${customer.ui.disabled:false}")
    private boolean isUiDisabledForCustomer;

    @Value("${display.hedge.booking.id:true}")
    private boolean isDisplayHedgeBookingId;

    @Value("${zombie.order.check.period.millis:60000}")
    private long zombieOrderCheckPeriodMillis;

    @Value("${zombie.order.threshold.millis:60000}")
    private long zombieOrderThresholdMillis;

    @Value("${hedging.close.retry.period.seconds:60}")
    private int hedgingCloserRetryPeriodInSeconds;

    @Value("${crosscurrency.evict.period.seconds:60}")
    private long crossCurrencyEvictPeriodSeconds;

    @Value("${findur.booking.disabled:false}")
    private boolean findurBookingDisabled;

    @Value("${fix.adapter.disabled:false}")
    private boolean fixAdapterDisabled;

    @Value("${hedge.businessunit.id}")
    private String hedgeBusinessUnitId;

    @Value("${autohedger.hssEnabled:false}")
    private boolean autohedgerHSSEnabled;

    @Value("${priceEngine.maxInputUpdatesPerSecondPerCP:100}")
    private long priceEngineMaxInputUpdatesPerSecondPerCP;

    @Value("${customerAppId:WTA4-UI}")
    private String customerAppId;

    @Value("${priceEngine.offlineStreamPeriodMillis:10000}")
    private Long priceEngineOfflineStreamPeriodMillis;

    @Value("${priceEngine.onlineStaticPriceStreamPeriodMillis:10000}")
    private Long priceEngineOnlineStaticPriceStreamPeriodMillis;

    @Value("${pushnotification.enabled:true}")
    private boolean pushNotificationEnabled;

    @Value("${autohedger.hss.heartBeatPeriod:5000}")
    private Long hssHeartBeatPeriodMillis;

    @Value("${autohedger.hss.maxProcessingDelay:3000}")
    private Long hssMaxProcessingDelayMillis;

    @Value("${amq.connectorUrl:tcp://127.0.0.1:6666}")
    private String amqBrokerURL;

    @Value("${maximum.deviation.on.offline.update:0.5}")
    private double maximumDeviationOnOfflineUpdate;

    @Value("${market.status.transition.notification:<EMAIL>}")
    private String marketStatusTransitionNotificationList;

    @Value("${market.status.transition.email.enabled:true}")
    private boolean isMarketStatusTransitionNotificationEnabled;

    @Value("${market.status.transition.schedule:SUNDAY 18:00:00,MONDAY 17:00:00,MONDAY 18:00:00,TUESDAY 17:00:00,TUESDAY 18:00:00,WEDNESDAY 17:00:00,WEDNESDAY 18:00:00,THURSDAY 17:00:00,THURSDAY 18:00:00,FRIDAY 17:00:00}")
    private String marketStatusTransitionSchedule;

    @Value("${market.status.transition.schedule.zoneId:America/New_York}")
    private String marketStatusTransitionScheduleZoneId;

    @Value("${system.user.id}")
    private String systemUserId;

    @Value("#{${offline.base.currencypairs:{'XAU/USD','XAG/USD','XPD/USD','XPT/USD','LPD/USD','LPT/USD','EUR/USD','USD/CHF','GBP/USD'}}}")
    private Set<String> offlineBaseCurrencyPairs;

    @Value("#{${offline.derived.currencypairs:{'XAU/GBP','XAU/EUR','XAU/CHF','XAG/GBP','XAG/EUR','XAG/CHF','XPD/GBP','XPD/EUR','XPD/CHF','XPT/GBP','XPT/EUR','XPT/CHF','LPD/GBP','LPD/EUR','LPD/CHF','LPT/GBP','LPT/EUR','LPT/CHF'}}}")
    private Set<String> offlineDerivedCurrencyPairs;

    @Value("${aggregated.booking.engine.check.period.seconds:60}")
    private long aggregatedBookingEngineCheckPeriodSeconds;

    @Value("${eod.customer.deal.recap.bu.list:}")
    private List<String> eodCustomerDealRecapBuList;

    @Value("${findur.oz.booking.enabled:false}")
    private boolean findurOZBookingEnabled;

    @Value("${findur.position.wta.order.cache.timetolive.millis:600000}") // 10 minutes
    private long findurPositionWTAOrderCacheTimeToLiveMillis;

    @Value("${findur.position.refresh.period.seconds:3600}") // 1 hour
    private long findurPositionRefreshPeriodSeconds;

    @Value("${exposure.engine.price.refresh.period.seconds:60}") // 1 minute
    private long exposureEnginePriceRefreshPeriodInSeconds;

    @Value("${exposure.feature.enabled:false}")
    private boolean isExposureFeatureEnabled;

    @Value("${eod.customer.deal.recap.cron:1 17 * * *}")
    private String customerDealRecapCronExpression;

    @Value("${eod.customer.deal.recap.cron.timezone:America/New_York}")
    private String customerDealRecapReportCronTimezone;

    @Value("${persistenceQueue.redelivery.initialRedeliveryDelay:10000}")
    private long persistenceQueueRedeliveryInitialRedeliveryDelay;

    @Value("${persistenceQueue.redelivery.useExponentialBackOff:true}")
    private boolean persistenceQueueRedeliveryUseExponentialBackoff;

    @Value("${persistenceQueue.redelivery.backOffMultiplier:2.0}")
    private double persistenceQueueRedeliveryBackOffMultiplier;

    @Value("${persistenceQueue.redelivery.maximumRedeliveries:10}")
    private int persistenceQueueRedeliveryMaximumRedeliveries;

    @Value("${persistenceQueue.redelivery.redeliveryDelay:10000}")
    private long persistenceQueueRedeliveryRedeliveryDelay;

	@Value("${eventBus.enabled:false}")
	private boolean eventBusEnabled;

    @Value("${eventBus.url:tcp://localhost:61616}")
    private String eventBusUrl;

    @Value("${eventBus.topicName:wta-synchronization}")
    private String eventBusTopicName;
    
    @Value("${primaryStatus:PRIMARY}")
    private PrimaryStatus primaryStatus;
    
    @Value("${eventBus.bookingQueueName:wta-booking}")
    private String eventBusBookingQueueName;

    @Value("${eventBus.bookingQueueName:wta-autohedger}")
    private String eventBusAutoHedgerQueueName;

    @Value("${eventBus.findurEventsTopicName:findur-events}")
    private String eventBusFindurEventsTopicName;
    
    //In property file: currency and comma separated list of dates in format YYYYMMDD, e.g., holiday.calendar.by.currency:{'XAU':'2026-01-01,2026-12-24', 'USD':'2026-01-01,2026-12-24'}
    @Value("#{${holiday.calendar.by.currency:{}}}")
    private Map<String, String> holidayCalendarByCurrency;
    
    private IHolidayCalendar holidayCalendar;
    
    @Autowired
    @Qualifier("rootBusinessUnitService")
    private RootBusinessUnitService rootBusinessUnitService;

    @Autowired
    private CurrencyPairService currencyPairService;

    @Autowired
    private BusinessUnitService businessUnitService;

    @Autowired
    private ProductService productService;

    @Autowired
    private UserService userService;

    @Autowired
    private BusinessUnitCurrencyPairService businessUnitCurrencyPairService;

    @Autowired
    private TradingCategoryCurrencyPairSpreadService tradingCategoryCurrencyPairSpreadService;

    @Autowired
    private ReferenceService referenceService;

    @Autowired
	private TradingCategoryService tradingCategoryService;

    @Autowired
    private LiquidityProviderService liquidityProviderService;

    @Autowired
    private HedgeProfileService hedgeProfileService;

  	@Autowired
  	private CurrencyPairEODService currencyPairEODService;

  	@Autowired
  	private CurrencyHolidayCalendarService currencyHolidayCalendarService;

  	@Autowired
  	private LPBandService lpBandService;

  	@Autowired
  	private MessageRecipientService emailRecipientService;

  	@Autowired
  	private CurrencyService currencyService;

  	@Autowired
  	private UserAgreementService userAgreementService;

  	@Autowired
  	private BUDistributionConfigurationService businessUnitChannelMDSConfigurationService;

    @Autowired
    private EvictCacheConfigurationService evictCacheConfigurationService;

    @Autowired
    private CacheRefreshInstructionService  cacheRefreshInstructionService;

    private AllowedOrderTypesResolver allowedOrderTypesResolver;

    private SyntheticSpreadResolver syntheticSpreadResolver;

    @Autowired
    private RFQConfigurationService rfqConfigurationService;

    @Autowired
    private OfflinePriceConfigurationService offlinePriceConfigurationService;

    @Autowired
    private FixSessionService fixSessionService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private StaticPriceService staticPriceService;

    @Autowired
    private BookingAggregationInstructionService bookingAggregationInstructionService;

    @Autowired
    private UserPreferenceService userPreferenceService;

    @Autowired
    private DeviceCredentialsService deviceCredentialsService;

    @Autowired
    private DropCrossedMDSResolver dropCrossedMDSResolver;
    
    @Autowired
    private ForwardCurveService forwardCurveService;

    @Override
    protected void startUp() throws Exception {
        try{
            LOG.info("startUp -> ");
            this.allowedOrderTypesResolver = new AllowedOrderTypesResolver(this, dealTicketAPIAllowedBUs, marketOrderAPIAllowedBUs);
            this.syntheticSpreadResolver = new SyntheticSpreadResolver(currencyPairService, tradingCategoryCurrencyPairSpreadService, crossCurrencyEvictPeriodSeconds);
            initHolidayCalendar();
            getAllCurrency();
            getAllCurrencyPairs();
            getAllProducts();
            getRootBusinessUnit();
            getActiveBusinessUnits();
            getLPBusinessUnits();
            getActiveUserAgreement();

            this.syntheticSpreadResolver.startSync();

            LOG.info("startUp <-");
        } catch(Exception e){
            LOG.error("startUp - ", e);
        }
    }

    private void initHolidayCalendar() {
       this.holidayCalendar = new MKSCustomHolidayCalendar(holidayCalendarByCurrency);        
    }

    @Override
    protected void shutDown() throws Exception {
        LOG.info("shutDown ->");
        this.syntheticSpreadResolver.stopSync();
        LOG.info("shutDown <-");
    }

	// Currency - START
	@Override
	public Set<Currency> getAllCurrency(){
		Set<Currency> currencyList = new HashSet<Currency>();
		List<String> currencyIdList = currencyService.getAllCurrency();
		for(String currencyId : currencyIdList) {
			Currency currencyr = currencyService.getCurrency(currencyId);
			currencyList.add(currencyr);
		}
		currencyService.setCurrencyMap(null);

		return currencyList;
	}

	@Override
	public Currency getCurrency(String currencyId) {
		Currency currency = currencyService.getCurrency(currencyId);
		return currency;
	}

	@Override
	public List<String> getSpreadDefinedCurrencyPair(){
		List<String> spreadDefinedCurrencyPairs = tradingCategoryCurrencyPairSpreadService.getSpreadDefinedCurrencyPair();
		return spreadDefinedCurrencyPairs;
	}

	// Currency - End

	// Currency Pair - START
	@Override
	public List<CurrencyPair> getAllCurrencyPairs() {
		List<String> allRootCurrencyPairsIds = currencyPairService.getAllCurrencyPairsIds();
		List<CurrencyPair> allCPS = new ArrayList<CurrencyPair>();
		for (String cp : allRootCurrencyPairsIds) {
			allCPS.add(currencyPairService.getCurrencyPair(cp));
		}
		return allCPS;

	}

	@Override
	public CurrencyPair getCurrencyPair(String currencyPairId) {
	    return currencyPairService.getCurrencyPair(currencyPairId);
	}


	// Currency Pair - END

	// Business Unit - START
	@Override
	public BusinessUnit getRootBusinessUnit() {
		return rootBusinessUnitService.getRootBusinessUnit();

	}


	@Override
	public List<BusinessUnit> getLPBusinessUnits() {
		List<BusinessUnit> businessUnitList = new ArrayList<BusinessUnit>();
		List<String> buIdList = businessUnitService.getLPBusinessUnits();
		for(String buId : buIdList) {
			BusinessUnit businessUnit = businessUnitService.getBusinessUnit(buId);
			businessUnitList.add(businessUnit);
		}
		businessUnitService.setLpBusinessUnitTblMap(null);
		return businessUnitList;
	}

	@Override
	@Cacheable("getAllCustomerBusinessUnits")
	public List<BusinessUnit> getAllBusinessUnits() {
		List<BusinessUnit> businessUnitList = new ArrayList<BusinessUnit>();
		List<String> buIdList = businessUnitService.getAllBusinessUnits();
		for(String buId : buIdList) {
			BusinessUnit businessUnit = businessUnitService.getBusinessUnit(buId);
			businessUnitList.add(businessUnit);
		}
		businessUnitService.setBusinessUnitTblMap(null);
		return businessUnitList;
	}

	@Override
	@Cacheable("getActiveCustomerBusinessUnits")
	public List<BusinessUnit> getActiveBusinessUnits() {
		List<BusinessUnit> businessUnitList = new ArrayList<BusinessUnit>();
		List<String> buIdList = businessUnitService.getActiveBusinessUnits();
		for(String buId : buIdList) {
			BusinessUnit businessUnit = businessUnitService.getBusinessUnit(buId);
			businessUnitList.add(businessUnit);
		}
		businessUnitService.setActiveBusinessUnitTblMap(null);
		return businessUnitList;
	}

	@Override
	public BusinessUnit getBusinessUnit(String buId) {
	    return businessUnitService.getBusinessUnit(buId);
	}

	@Override
	public BusinessUnit getLPITABusinessUnit() {
		return rootBusinessUnitService.getRootBusinessUnit();
	}


	@Override
	public BusinessUnit getHedgingBusinessUnit() {
	    BusinessUnit hedgeBusinessUnit = businessUnitService.getBusinessUnit(hedgeBusinessUnitId);
	    return hedgeBusinessUnit;
	}

	@Override
	public List<BusinessUnit> getBusinessUnitsByTradingCategory(String categoryId) {
		List<BusinessUnit> businessUnitsList = new ArrayList<BusinessUnit>();
		List<String> buIds =businessUnitService.getBusinessUnitsByTradingCategory(categoryId);
		for(String buId : buIds) {
			BusinessUnit businessUnit = new BusinessUnit();
			businessUnit = businessUnitService.getBusinessUnit(buId);
			businessUnitsList.add(businessUnit);
		}
		return businessUnitsList;
	}


	@Override
	public List<BusinessUnit> getBusinessUnitByUser(Integer userId) {
		 List<BusinessUnit> buList = null;
		 List<String> buIds=businessUnitService.getBusinessUnitByUser(userId);
	      if (buIds != null && buIds.size() > 0) {
	          buList = new ArrayList<BusinessUnit>();
	          for (String buId : buIds) {
	            BusinessUnit bu = businessUnitService.getBusinessUnit(buId);
	            if(null != bu) {
	            	buList.add(bu);
	            }
	          }
	        }

		return buList;
	}

	// Business Unit - END

	// Product - START
	@Override
	public List<Product> getAllProducts(){
		List<Product> productList = new ArrayList<Product>();
		List<String> productIdList = productService.getAllProducts();
		for(String productId : productIdList) {
			productList.add(productService.getProduct(productId));
		}
		return productList;
	}

	@Override
	public Product getProduct(String productId) {
		Product product = productService.getProduct(productId);
		return product;
	}

	// Product - END

	// User - START
	@Override
	public User getUser(String userId) {
		User user = userService.getUser(userId);
		return user;
	}

	@Override
	public List<User> getUsersByBusinessUnit(String buId) {
		List<User> userList = new ArrayList<User>();
		List<User> users = userService.getMinimalUsersByBusinessUnit(buId);
		for(User user : users) {
			User fullUser = userService.getUser(user.getUserId());
			if(null != fullUser) {
				userList.add(userService.getUser(user.getUserId()));
			}
		}
		return userList;
	}

	@Override
	public User getSystemUser() {
		User user = userService.getUser(systemUserId);
		return user;
	}

	@Override
	public UserAgreement getActiveUserAgreement() {
		UserAgreement agreement = userAgreementService.getActiveUserAgreement();
		return agreement;
	}

	// User - END

	// Band and Spread - START
	@Override
	public List<Double> getBands(String currencyPairId) {
		return syntheticSpreadResolver.getBands(currencyPairId);
	}

	@Override
	public Spread getSpread(String categoryId, String currencyPairId) {
	    return syntheticSpreadResolver.getSpread(categoryId, currencyPairId);
	}

    @Override
	public Double getMinimumSpread(String currencyPairId) {
	    return syntheticSpreadResolver.getMinimumSpread(currencyPairId);
	}
	// Band and Spread - END

	// Hedge Profile - START

    @Override
    public HedgeProfile getHedgeProfile(String currencyPairId, OrderType orderType) {
        return hedgeProfileService.getHedgingProfileByOrderTypeAndCurrencyPair(orderType.name(), currencyPairId);
    }

	@Override
	public Product getHedingProduct(String currencyPair) {
		HedgeProfile hedgeProfile = hedgeProfileService.getHedgingProfileByOrderTypeAndCurrencyPair("FOK", currencyPair);
		Product product = productService.getProduct(hedgeProfile.getHedgeProductId());
		return product;
	}

	@Override
	public List<HedgeProfile> getHedgingProfileByOrderType(OrderType orderType) {
		List<HedgeProfile> hedgeProileListToreturn = hedgeProfileService.getHedgingProfileByOrderType(orderType.name());
		return hedgeProileListToreturn;
	}

	@Override
	public Map<String, HedgingConfiguration> getFOKHedgingConfigurationsByCurrencyPair() {
		List<HedgeProfile> hedgeProfiles = hedgeProfileService.getHedgingProfileByOrderType(OrderType.FOK.toString());
		return hedgeProfiles.stream().collect(
			Collectors.toMap(p -> p.getCurrencyPairId(), p -> new HedgingConfiguration(p.getHedgingMode(), p.getHedgingOperation()))
		);
	}

	@Override
	public List<HedgeProfile> getAllHedgingProfiles() {
		return hedgeProfileService.getAllHedgingProfiles();
	}
	// Hedge Profile - END


	// Liquidity Provide - START
	@Override
	public List<LiquidityProvider> getLiquidityProviders() {
		List<LiquidityProvider> lpList = liquidityProviderService.getLiquidityProviders();
		return lpList;
	}
	// Liquidity Provide - END

	@Override
	public List<Reference> getReferenceListByType(ReferenceType type) {
		List<Reference> referenceList = referenceService.getReferenceListByType(type.name());
		return referenceList;
	}

	@Override
	public Reference getReferenceByTypeAndValue(ReferenceType type, String value) {
		Reference reference = referenceService.getReferenceByTypeAndValue(type.name(), value);
		return reference;
	}

	@Override
	public Reference getReferenceByTypeAndKey(ReferenceType type, ReferenceKey key) {
		Reference reference = referenceService.getReferenceByTypeAndKey(type.name(), key.name());
		return reference;
	}

	@Override
	public long getOrderMonitorGTDPollTimeMillis() {
		return 10000;
	}


	@Override
	public int getLogonWaitTimeInSeconds() {

		Integer logonWaitTime = null;

		Reference referenceTbl =  referenceService.getReferenceByTypeAndKey(WTA4Constants.REFERENCE_TYPE_ID_WAIT_TIME,
				WTA4Constants.REFERENCE_KEY_LOGON_WAIT_TIME);
    	if(null != referenceTbl) {
    		Integer.parseInt(referenceTbl.getValue());
    	}
    	else {
    		logonWaitTime = WTA4Constants.DEFAULT_LOGON_WAIT_TIME;
    	}
    	return logonWaitTime;
	}

	@Override
	@Cacheable("getAuctionMinimumMinutesToSessionTime")
	public Long getAuctionMinimumMinutesToSessionTime(String currencyPairId) {
		return AUCTION_DEFAULT_MINIMUM_MINUTES_TO_SESSION_TIME;
	}

	@Override
	public BUDistributionConfiguration getBUDistributionConfiguration(String buId, Channel channel) {
		return businessUnitChannelMDSConfigurationService.getBusinessUnitDistributionConfiguration(buId, channel.name());
	}

	@Override
    public TimeWithZoneId getEODHedgingClosingTime(String currencyPairId) {
       return currencyPairEODService.getEODHedgingClosingTime(currencyPairId);
    }

	@Override
	@Cacheable("getOverridenPipConfiguration")
	public PipConfiguration getOverriddenPipConfiguration(String buId, String currencyPairId) {
		LOG.info("getOverridenPipConfiguration -> buId={}, currencyPairId={}",buId,currencyPairId);
		PipConfiguration pipConfig = businessUnitCurrencyPairService.getBuCurrencyPairPipConfiguration(buId, currencyPairId);
		LOG.info("getOverridenPipConfiguration <- buId={}, currencyPairId={}, pipConfig={}",buId,currencyPairId,pipConfig);
		return pipConfig;
	}


    @Override
	@Cacheable("getOverriddenPriceVariationThreshold")
	public Double getOverriddenPriceVariationThreshold(String buId, String currencyPairId) {
		LOG.info("getOverriddenPriceVariationThreshold -> buId{},currencyPairId={}", buId, currencyPairId);
		Double pvtLevel = businessUnitCurrencyPairService.getBuCurrencyPairPvt(buId,currencyPairId);
		LOG.info("getOverriddenPriceVariationThreshold <- buId{},currencyPairId={},pvtLevel={}", buId, currencyPairId,pvtLevel);
		return pvtLevel;
	}



	@Override
	public Double getOrderPriceCheckMargin(String currencyPairId, OrderType type) {
		LOG.info("getOrderPriceCheckMargin -> currencyPairId{},type={}",currencyPairId,type);
		Double orderPriceThreshold = null;
		CurrencyPair currencyPair = currencyPairService.getCurrencyPair(currencyPairId);
		if(null != currencyPair) {
			orderPriceThreshold = currencyPair.getOrderPriceCheckMargin();
		}
		LOG.info("getOrderPriceCheckMargin <- currencyPairId={}, type={}, result={}", currencyPairId, type, orderPriceThreshold.doubleValue());
		return orderPriceThreshold != null ? orderPriceThreshold : 0.0;
	}

	@Override
	public int getHedgingCloserMaxRetries() {
		 return 2;
	}

	@Override
	public int getHedgingCloserRetryPeriodInSeconds() {
		return hedgingCloserRetryPeriodInSeconds;
	}

	@Override
	public long getOrderMinimumTimeSeparationInMillis() {
		Reference timeIntervalVO =  referenceService.getReferenceByTypeAndKey("TIME_INTERVAL", "BU_CP_TRX_TIME_INTERVAL");
		if (timeIntervalVO != null) {
			return Integer.parseInt(timeIntervalVO.getValue()) * 1000;
		} else {
			return 0;
		}
	}

	@Override
	@Cacheable("getMarketStatusSchedule")
	public MarketStatusSchedule getMarketStatusSchedule() {
		LOG.debug("getMarketStatusSchedule ->");
		MarketStatusSchedule marketStatusSchedule = MarketStatusScheduleFactory.create( marketStatusTransitionSchedule, ZoneId.of(marketStatusTransitionScheduleZoneId) );
		LOG.debug("getMarketStatusSchedule <- marketStatusSchedule={}", marketStatusSchedule);
        return marketStatusSchedule;
	}

	@Override
	public Double getAuctionCommission(String buId, String currencyPairId, Operation mksOperation) {
		LOG.info("getAuctionComission->buId={},currencyPairId={},operation={}", buId, currencyPairId, mksOperation);
		Double auctionCommission = businessUnitCurrencyPairService.getAuctionCommission(buId, currencyPairId,
				mksOperation.name());

		if (auctionCommission == null) {
			if (mksOperation == Operation.SELL) {
				CurrencyPair currencyPair = currencyPairService.getCurrencyPair(currencyPairId);
				auctionCommission = currencyPair.getAuctionCommissionOffer() != null
						? currencyPair.getAuctionCommissionOffer().doubleValue() : null;
			}
			if (mksOperation== Operation.BUY) {
				CurrencyPair currencyPair = currencyPairService.getCurrencyPair(currencyPairId);
				auctionCommission = currencyPair.getAuctionCommissionBid() != null
						? currencyPair.getAuctionCommissionBid().doubleValue() : null;
			}
		}

		LOG.info("getAuctionComission <- auctionCommission={}", auctionCommission);
		return auctionCommission;
	}

	@Override
    public List<LocalDate> getUKAuctionHolidays(String currencyId, SpecificTime specificTime) {
		LOG.debug("getUKAuctionHolidays-> currencyId={},specificTime={} ", currencyId,specificTime);
        return currencyHolidayCalendarService.getUKAuctionHolidays(currencyId, specificTime.name());
    }

	private static TimeWithZoneId EOD_GVA = new TimeWithZoneId(LocalTime.of(0,0,0), TimeUtils.GVA_ZONE_ID);
	@Override
	public TimeWithZoneId getEODTime() {
		return EOD_GVA;
	}

	@Override
	public List<User> getMessageRecipientList(String buId, MessageType messageType){
		LOG.debug("getMessageRecipientList -> buId={}, messageType={}", buId, messageType);
		return emailRecipientService.getMessageRecipientList(buId, messageType.toString());
	}

	@Override
	public ZoneId getApplicationZoneId() {
		 return ZoneId.systemDefault();
	}

	@Override
	public String getActiveOrderListCronExpression() {
		Reference reference =  referenceService.getReferenceByTypeAndKey("CRON_EXPRESSION", "ACTIVE_ORDER");
    	if(null != reference) {
    		return reference.getValue();
    	}

        return "*/15 * * * mon-fri";
    	//return "* * 0/3 * * mon-fri";
	}


	public String getEODRecapCronExpression() {
		Reference reference =  referenceService.getReferenceByTypeAndKey("CRON_EXPRESSION", "EOD_RECAP");
    	if(null != reference) {
    		return reference.getValue();
    	}
    	return "1 0 * * *";
	}


	@Override
	public String getDealPendingBookingAlertCronExpression() {
		// "*/10 * * * mon-fri"
		String cron = null;
		Reference reference =  referenceService.getReferenceByTypeAndKey("CRON_EXPRESSION", "DEAL_PENDING_BOOKING_ALERT");
    	if(null != reference && null != reference.getValue()) {
    		cron=  reference.getValue();
    	}

        return cron;
	}

	@Override
	//@Cacheable("getOrderStaleCheckPeriodInSeconds")
	public long getOrderStaleCheckPeriodInSeconds() {
		LOG.debug("getOrderStaleCheckPeriodInSeconds -> ");
		Reference reference =  referenceService.getReferenceByTypeAndKey("TIME_INTERVAL", "ORDER_STALE_CHECK_PERIOD");
    	if(null != reference) {
    		LOG.debug("getOrderStaleCheckPeriodInSeconds <- ",reference.getValue());
    		return Integer.parseInt(reference.getValue());
    	}

		LOG.debug("getOrderStaleCheckPeriodInSeconds <-  60");
        return 60;
	}

	@Override
	public long getMaxStalledTimeInSeconds() {
		LOG.debug("getMaxStalledTimeInSeconds -> ");
		Reference reference =   referenceService.getReferenceByTypeAndKey("TIME_INTERVAL", "MAX_STALLED_TIME");
    	if(null != reference) {
    		LOG.debug("getMaxStalledTimeInSeconds <- reference={}",reference.getValue());
    		return Integer.parseInt(reference.getValue());
    	}
		LOG.debug("getMaxStalledTimeInSeconds <- 60");
        return 60;
	}

	public Double[] getLPBands(String currencyPairId) {
		return lpBandService.getLPBands(currencyPairId);
	}

	@Override
	public String getApplicationId() {
		return applicationId;
	}

	@Override
	public String getSequentialIDGeneratorStoreFilePath() {
		return sequentialIDGeneratorStoreFile;
	}

	@Override
    public String getMDSRepositoryStoreFile() {
        return mdsRegisterStoreFile;
    }

	@Override
	public String getProjectBuildType() {
		return projectBuildType;
	}

    @Override
    public int getMaxFailedLoginAttempts() {
        Reference ref = this.getReferenceByTypeAndKey(ReferenceType.PASSWORD_POLICY, ReferenceKey.PWD_INVALID_ATTEMPT);
        if ( ref == null ) {
            return 5; // default
        } else {
            return Integer.parseInt(ref.getValue());
        }
    }

    @Override
    @Cacheable("getAllowedOrderTypes") // there is no evict because data is loaded from properties and requires restart anyway
    public List<OrderType> getAllowedOrderTypes(String buId, Channel channel) {
        return allowedOrderTypesResolver.getAllowedOrderTypes(buId, channel);
    }

    @Override
    public boolean isEODClosingActivated() {
        return isEODClosingActivated;
    }


    @Override
    public boolean isUiDisabledForCustomer() {
        return isUiDisabledForCustomer;
    }

    @Override
	public boolean isDisplayHedgeBookingId() {
		return isDisplayHedgeBookingId;
	}

	@Override
	public void evictBusinessUnit(String buId) {
		evictCacheConfigurationService.evictBusinessUnit(buId);
	}

	@Override
	public void evictUser(String userId) {
		evictCacheConfigurationService.evictUser(userId);
	}

	@Override
	public void evictBand(String currencyPair) {
		evictCacheConfigurationService.evictBand(currencyPair);
	}


	@Override
	public void evictLiquidityProviders() {
		evictCacheConfigurationService.evictLiquidityProviders();
	}

	@Override
	public void evictCurrencyPair(String currencyPairId) {
		evictCacheConfigurationService.evictCurrencyPair(currencyPairId);
	}

	@Override
	public void evictProduct(String productId) {
		evictCacheConfigurationService.evictProduct(productId);
	}

	@Override
	public void evictSpread(String categoryId, String currencyPairId) {
		evictCacheConfigurationService.evictSpread(categoryId, currencyPairId);
		syntheticSpreadResolver.evictSpreadCache(categoryId, currencyPairId);
	}

	@Override
	public void evictReferenceListByType(String orderType) {
		evictCacheConfigurationService.evictReferenceListByType(orderType);
	}

	@Override
	public void evictAuctionMinimumMinutesToSessionTime(String currencyPairId) {
		evictCacheConfigurationService.evictAuctionMinimumMinutesToSessionTime(currencyPairId);
	}

	@Override
	public void evictOverriddenPipConfiguration(String buId, String currencyPairId) {
		evictCacheConfigurationService.evictOverriddenPipConfiguration(buId, currencyPairId);
	}

	@Override
	public void evictOverriddenPriceVariationThreshold(String buId, String currencyPairId) {
		evictCacheConfigurationService.evictOverriddenPriceVariationThreshold(buId, currencyPairId);
	}

	@Override
	public void evictMarketStatusSchedule() {
		evictCacheConfigurationService.evictMarketStatusSchedule();
	}

	@Override
	public void evictAuctionCommission(String buId, String currencyPairId, String mksOperation) {
		evictCacheConfigurationService.evictAuctionCommission(buId, currencyPairId, mksOperation);
	}

	@Override
	public void evictUKAuctionHolidays(String currencyPairId, String specificTime) {
		evictCacheConfigurationService.evictUKAuctionHolidays(currencyPairId, specificTime);
	}

	@Override
	public void evictMessageRecipientList(String buId, String messageType) {
		evictCacheConfigurationService.evictMessageRecipientList(buId, messageType);
	}

	@Override
	public void evictOrderStaleCheckPeriodInSeconds() {
		evictCacheConfigurationService.evictOrderStaleCheckPeriodInSeconds();
	}

	@Override
	public void evictMaxStalledTimeInSeconds() {
		evictCacheConfigurationService.evictMaxStalledTimeInSeconds();
	}

	@Override
	public void evictEODHedgingClosingTime(String currencyPairId) {
		evictCacheConfigurationService.evictEODHedgingClosingTime(currencyPairId);
	}

	@Override
	public void evictUnitOfMeasureByCode(String code) {
		evictCacheConfigurationService.evictUnitOfMeasureByCode(code);
	}

	@Override
	public void evictActiveUserAgreement() {
		evictCacheConfigurationService.evictActiveUserAgreement();
	}

	@Override
	public void evictHedgingProfileByOrderType(String orderType) {
		evictCacheConfigurationService.evictHedgingProfileByOrderType(orderType);
	}

	@Override
	public void evictApplyMinimumSpread(String categoryId) {
		evictCacheConfigurationService.evictApplyMinimumSpread(categoryId);
	}

	@Override
	public void evictCurrency(String currencyId) {
		evictCacheConfigurationService.evictCurrency(currencyId);
	}

	@Override
	public void evictLPBands(String currencyPairId) {
		evictCacheConfigurationService.evictLPBands(currencyPairId);
	}

	@Override
	public void evictReferenceByTypeAndValue(String type, String value) {
		evictCacheConfigurationService.evictReferenceByTypeAndValue(type, value);
	}

	@Override
	public void evictReferenceByTypeAndKey(String type, String key) {
		evictCacheConfigurationService.evictReferenceByTypeAndKey(type, key);
	}

	@Override
	public void evictRootBusinessUnit() {
		evictCacheConfigurationService.evictRootBusinessUnit();
	}

	@Override
	public void evictBusinessUnitByUser(Integer userId) {
		evictCacheConfigurationService.evictBusinessUnitByUser(userId);
	}

	@Override
	public void evictAllBusinessUnits() {
		evictCacheConfigurationService.evictAllBusinessUnits();
	}

	@Override
	public void evictBusinessUnitsByTradingCategory(String categoryId) {
		evictCacheConfigurationService.evictBusinessUnitsByTradingCategory(categoryId);
	}

	@Override
	public void evictBusinessUnitDistributionConfiguration(String buId, String channel) {
		evictCacheConfigurationService.evictBusinessUnitDistributionConfiguration(buId, channel);
	}

	@Override
	public void evictUserProfileByUserId(int userId) {
		evictCacheConfigurationService.evictUserProfileByUserId(userId);
	}

	@Override
	public void evictMinimalUsersByBusinessUnit(String buId) {
		evictCacheConfigurationService.evictMinimalUsersByBusinessUnit(buId);
	}

	@Override
	public void evictAllCurrencyPairs() {
		evictCacheConfigurationService.evictAllCurrencyPairs();
		evictCacheConfigurationService.evictRootBusinessUnit();
	}

	@Override
	public void evictAllProducts() {
		evictCacheConfigurationService.evictAllProducts();
	}

	@Override
	public void evictAllCurrency() {
		evictCacheConfigurationService.evictAllCurrency();
	}

	@Override
	public String getBuIdByPK(Integer buId) {
		return businessUnitService.getBusinessUnitIdByPK(buId);
	}

	@Override
	public String getUserIdByPK(Integer userId) {
		return userService.getUserIdByPK(userId);
	}

	@Override
	public Set<CacheRefreshInstruction> getAllCacheRefreshInstruction(Date datetime) {
		return cacheRefreshInstructionService.findAllUpdatedSince(datetime);
	}

	@Override
	public Integer getBUPKByUserIdByPK(Integer userId) {
		return userService.getBUPKByUserIdByPK(userId);
	}

	@Override
	public List<User> getMinimalUsersByBusinessUnit(String buId) {
		return userService.getMinimalUsersByBusinessUnit(buId);
	}

    @Override
    public long getZombieOrderCheckPeriodMillis() {
        return zombieOrderCheckPeriodMillis;
    }

    @Override
    public long getZombieOrderThresholdMillis() {
        return zombieOrderThresholdMillis;
    }

    public void setPriceProvider(IMarketDataSnapshotProvider priceProvider) {
        this.syntheticSpreadResolver.setPriceProvider(priceProvider);
    }

    @Override
	public boolean isFindurBookingDisabled() {
		return findurBookingDisabled;
	}

	public void setFindurBookingDisabled(boolean findurBookingDisabled) {
		this.findurBookingDisabled = findurBookingDisabled;
	}

	@Override
    public RFQConfiguration getRFQConfiguration(String buId, String currencyPairId) {
    	RFQConfiguration rfqConfiguration = rfqConfigurationService.getRFQConfiguration(buId, currencyPairId);
    	return rfqConfiguration;
    }

    @Override
    public OfflinePriceConfiguration getOfflinePriceConfiguration(String currencyPairId) {
    	OfflinePriceConfiguration offlinePriceConfiguration = offlinePriceConfigurationService.getOfflinePriceConfiguration(currencyPairId);
    	return offlinePriceConfiguration;
    }

    @Override
    public List<OfflinePriceConfiguration> getAllOfflinePriceConfiguration() {
    	List<OfflinePriceConfiguration> allOfflinePriceConfiguration = offlinePriceConfigurationService.getAllOfflinePriceConfiguration();
    	return allOfflinePriceConfiguration;
    }

	@Override
	public void evictRFQConfiguration(String buId, String currencyPairId) {
		// TODO Auto-generated method stub

	}
	@Override
	public List<FixSessionConfiguration> getActiveFixSessions() {
		return fixSessionService.getActiveFixSessions();
	}
  	@Override
	public DefaultFixConfiguration getDefaultFixConfiguration() {
		return fixSessionService.getDefaultFixConfiguration();
	}

	@Override
	public void evictHedgingProfileByOrderTypeAndCurrencyPair(String orderType, String currencyPairId) {
	    evictCacheConfigurationService.evictHedgingProfileByOrderTypeAndCurrencyPair(orderType, currencyPairId);
	}

    @Override
    public List<Device> getUserDevices(String userId,String appId) {
        return deviceService.getDevicesByUserId(userId,appId);
    }

    @Override
	public void evictUserDevice(String userId,String appId) {
		evictCacheConfigurationService.evictUserDevice(userId,appId);
	}

	@Override
	public Long getPriceEngineMaxInputUpdatesPerSecondPerCP() {
	    return priceEngineMaxInputUpdatesPerSecondPerCP;
	}

	@Override
	public String getCustomerAppId() {
	    return customerAppId;
	}

	@Override
	public Long getPriceEngineOfflineStreamPeriodMillis() {
	    return priceEngineOfflineStreamPeriodMillis;
	}

	@Override
	public boolean isPushNotificationEnabled() {
	    return pushNotificationEnabled;
	}

    @Override
    public String getAMQBrokerURL() {
        return amqBrokerURL;
    }

	@Override
    public double getMaximumDeviationOnOfflineUpdate() {
    	return maximumDeviationOnOfflineUpdate;
    }

	@Override
	public String getMarketStatusTransitionNotificationList() {
		return marketStatusTransitionNotificationList;
	}

	@Override
	public boolean getIsMarketStatusTransitionEnabled() {
		return isMarketStatusTransitionNotificationEnabled;
	}

    @Override
    public Set<String> getOfflineBaseCurrencyPairs() {
        return offlineBaseCurrencyPairs;
    }

    @Override
    public Set<String> getOfflineDerivedCurrencyPairs() {
        return offlineDerivedCurrencyPairs;
    }

	@Override
	public List<Category> getAllTradingCategories() {
		return tradingCategoryService.getTradingCategories();
	}

	@Override
	public List<AuctionCommissionOverride> getAllAuctionCommissionOverrides() {
		return businessUnitCurrencyPairService.getAllAuctionCommissionOverrides();
	}

	@Override
    public List<PriceVariationThresholdOverride> getAllPriceVariationThresholdOverrides() {
        return businessUnitCurrencyPairService.getAllPVTOverrides();
    }

    @Override
    public StaticPrice getStaticPrice(String currencyPairId, StaticPriceType type) {
        return staticPriceService.getStaticPrice(currencyPairId, type.name());
    }

    @Override
    public List<StaticPrice> getAllStaticPricesOfType(StaticPriceType type) {
        return staticPriceService.getAllStaticPricesOfType(type.name());
    }

    @Override
    public Long getPriceEngineOnlineStaticPriceStreamPeriodMillis() {
        return priceEngineOnlineStaticPriceStreamPeriodMillis;
    }

	@Override
	public void evictStaticPrice(String currencyPairId, String type) {
		evictCacheConfigurationService.evictStaticPrice(currencyPairId, type);
	}

	@Override
	public void evictAllStaticPricesOfType(String type) {
		evictCacheConfigurationService.evictAllStaticPricesOfType(type);
	}

	@Override
	public String getCurrencyPairIdBySymbolAndLocation(String symbol, Location location) {
		return currencyPairService.getCurrencyPairIdBySymbolAndLocation(symbol, location.name());
	}

	@Override
	public void evictAllHedgingProfiles() {
		evictCacheConfigurationService.evictAllHedgingProfiles();
	}

	@Override
	public void evictRootBusinessUnitCurrencyPair(CurrencyPair currencyPair) {
		evictCacheConfigurationService.evictRootBusinessUnitCurrencyPair(currencyPair);
	}

	@Override
	public List<BookingAggregationInstruction> getBookingAggregationInstructions() {
	   return  bookingAggregationInstructionService.getAllBookingAggregationInstruction();
	}

    @Override
    public List<String> getOfflineTradingAllowedBUList() {
        return offlineTradingAllowedBUs;
    }

    @Override
    public long getAggregatedBookingEngineCheckPeriodSeconds() {
        return aggregatedBookingEngineCheckPeriodSeconds;
    }
    @Override
    public void evictAllBookingAggregationInstruction() {
        evictCacheConfigurationService.evictAllBookingAggregationInstruction();
    }

    @Override
    public String getUserPreferences(String appId, String userId, Channel channel, UserPreferenceType userPreferenceType) {
        return userPreferenceService.getUserPreferences(appId, userId, channel, userPreferenceType);
    }

    @Override
    public String getDeviceCredentials(String appId, String deviceId) {
        return deviceCredentialsService.getUserPreferences(appId, deviceId);
    }

    @Override
    public boolean shouldWeDropCrossedPrices() {
        return dropCrossedMDSResolver.shouldWeDropCrossedPrices();
    }

	@Override
	public Double getCrossedPriceThresholdPct() {
		return dropCrossedMDSResolver.getcrossedPriceThresholdPctValue();
	}

    @Override
    public List<String> getStaleQuoteExcludedBUList() {
        return staleCheckExcludedBUs;
    }

    @Override
    public List<String> getPVTExcludedBUList() {
        return pvtCheckExcludedBUs;
    }

	@Override
	public List<String> getBUListForEODCustomerDealRecap() {
		return eodCustomerDealRecapBuList;
	}

    public boolean isFindurOZBookingEnabled() {
        return findurOZBookingEnabled;
    }

    @Override
    public List<BUDistributionConfiguration> getAllBUDistributionConfigurations() {
       return  businessUnitChannelMDSConfigurationService.getAllBusinessUnitDistributionConfigurations();
    }

    @Override
    public long getFindurPositionWTAOrderCacheTimeToLiveMillis() {
        return findurPositionWTAOrderCacheTimeToLiveMillis;
    };

    @Override
    public long getFindurPositionRefreshPeriodSeconds() {
        return findurPositionRefreshPeriodSeconds;
    };

    @Override
    public long getExposureEnginePriceRefreshPeriodInSeconds() {
        return exposureEnginePriceRefreshPeriodInSeconds;
    };

    @Override
    public boolean isExposureFeatureEnabled() {

        return isExposureFeatureEnabled;
    }

    @Override
    @Cacheable("getBusinessUnitCurrencyPairs")
    public CurrencyPair getBusinessUnitCurrencyPair(String buId, String currencyPairId) {
        if (getBusinessUnit(buId) != null) {
            return getBusinessUnit(buId).getCurrencyPairs().stream().filter(cp -> cp.getCurrencyPairId().equals(currencyPairId)).findFirst().orElse(null);
        } else {
            return null;
        }
    }

	@Override
	public String getCustomerEODRecapCronExpression() {
		return customerDealRecapCronExpression;
	}

	@Override
	public ZoneId getCustomerEODRecapCronTimeZoneId() {
		return ZoneId.of(customerDealRecapReportCronTimezone);
	}

	@Override
    public long getPersistenceQueueRedeliveryInitialRedeliveryDelay() {
		return this.persistenceQueueRedeliveryInitialRedeliveryDelay;
	}

	@Override
    public boolean isPersistenceQueueRedeliveryUseExponentialBackoff() {
		return this.persistenceQueueRedeliveryUseExponentialBackoff;
	}

	@Override
    public double getPersistenceQueueRedeliveryBackOffMultiplier() {
		return this.persistenceQueueRedeliveryBackOffMultiplier;
	}

	@Override
    public int getPersistenceQueueRedeliveryMaximumRedeliveries() {
		return this.persistenceQueueRedeliveryMaximumRedeliveries;
	}

	@Override
    public long getPersistenceQueueRedeliveryRedeliveryDelay() {
		return this.persistenceQueueRedeliveryRedeliveryDelay;
	}

	@Override
	public String getBuCurrencyPairIdByPK(Integer Id) {
		return businessUnitCurrencyPairService.getBuCurrencyPairIdByPK(Id);
	}

	@Override
	public void evictBusinessUnitCurrencyPair(String buId, String currencyPairId) {
		evictCacheConfigurationService.evictBusinessUnitCurrencyPair(buId, currencyPairId);
	}

	@Override
	public Boolean getEventBusEnabled() {
		return this.eventBusEnabled;
	}

	@Override
    public String getEventBusURL() {
        return this.eventBusUrl;
    }

    @Override
    public String getEventBusTopicName() {
        return this.eventBusTopicName;
    }

    @Override
    public PrimaryStatus getPrimaryStatus() {
        return primaryStatus;
    }

    @Override
    public String getEventBusBookingQueueName() {
        return eventBusBookingQueueName;
    }
    
    @Override
    public String getEventBusAutoHedgerQueueName() {
        return eventBusAutoHedgerQueueName;
    }

    @Override
    public String getEventBusFindurEventsTopicName() {
        return eventBusFindurEventsTopicName;
    }

    @Override
    public IHolidayCalendar getHolidayCalendar() {
        return holidayCalendar;
    }

	@Override
	public List<ForwardCurve> getAllForwardCurves() {
		return forwardCurveService.getAllForwardCurves();
	}

	@Override
	public ForwardCurve getForwardCurve(String currencyPairId) {
		return forwardCurveService.getForwardCurve(currencyPairId);
	}

	@Override
	public void evictAllForwardCurves() {
		evictCacheConfigurationService.evictAllForwardCurves();
	}

	@Override
	public void evictForwardCurve(String currencyPairId) {
		evictCacheConfigurationService.evictForwardCurve(currencyPairId);
	}
}
