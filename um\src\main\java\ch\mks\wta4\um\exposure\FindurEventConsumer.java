package ch.mks.wta4.um.exposure;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.json.JsonMapper;
import com.mkspamp.eventbus.client.IEventBusConsumer;
import com.mkspamp.eventbus.client.IEventBusMessageListener;
import com.mkspamp.eventbus.client.amq.AMQEventBusBase.DestinationType;
import com.mkspamp.eventbus.client.amq.AMQEventBusConsumer;
import com.mkspamp.eventbus.model.Event;
import com.mkspamp.eventbus.serialization.DataModelJacksonMapperFactory;

import ch.mks.wta4.common.metrics.MicrometerService;
import ch.mks.wta4.common.service.AbstractWTA4Service;
import ch.mks.wta4.configuration.IConfiguration;
import io.micrometer.core.instrument.Counter;

/**
 * Consumer for Findur events from the event bus.
 * This service listens to Findur events and notifies registered listeners.
 *
 * Features:
 * - Receives all types of Findur events from the event bus
 * - Notifies registered listeners asynchronously
 * - Provides Micrometer metrics for monitoring event processing
 */
public class FindurEventConsumer extends AbstractWTA4Service implements IEventBusMessageListener {

    /**
     * Interface for listening to Findur events from the FindurEventConsumer.
     * Implementations of this interface can be registered with the FindurEventConsumer
     * to receive notifications when Findur events are received.
     */
    public interface IFindurEventConsumerListener {

        /**
         * Called when a Findur event is received by the FindurEventConsumer.
         *
         * @param event The Findur event that was received
         */
        void onFindurEvent(Event event);
    }

    private static final Logger LOG = LoggerFactory.getLogger(FindurEventConsumer.class);

    private final IConfiguration configuration;
    private final JsonMapper jsonMapper;
    private final List<IFindurEventConsumerListener> listeners;
    
    private IEventBusConsumer eventBusConsumer;
    
    // Micrometer metrics
    private Counter eventsReceivedCounter;
    private Counter eventsProcessedCounter;
    private Counter processingErrorsCounter;

    /**
     * Constructor for dependency injection
     * 
     * @param configuration Configuration service for event bus settings
     */
    public FindurEventConsumer(IConfiguration configuration) {
        this.configuration = configuration;
        this.listeners = new ArrayList<>();
        // Create JsonMapper using DataModelJacksonMapperFactory for proper Event deserialization
        this.jsonMapper = new DataModelJacksonMapperFactory().create(false); // No pretty printing for performance
        
        // Initialize Micrometer counters
        this.eventsReceivedCounter = MicrometerService.getCounter("findur.events.received", "component", "FindurEventConsumer");
        this.eventsProcessedCounter = MicrometerService.getCounter("findur.events.processed", "component", "FindurEventConsumer");
        this.processingErrorsCounter = MicrometerService.getCounter("findur.events.errors", "component", "FindurEventConsumer");
    }

    /**
     * Adds a listener to receive Findur event notifications
     * 
     * @param listener The listener to add
     */
    public void addListener(IFindurEventConsumerListener listener) {
        if (listener != null) {
            synchronized (listeners) {
                listeners.add(listener);
            }
            LOG.info("addListener - added listener: {}", listener.getClass().getSimpleName());
        }
    }

    @Override
    protected void startUp() throws Exception {
        if (!configuration.isExposureFeatureEnabled()) {
            LOG.info("startUp - exposure feature is not enabled. Doing nothing");
            return;
        }

        if (!configuration.getEventBusEnabled()) {
            LOG.info("startUp - event bus is not enabled. Doing nothing");
            return;
        }

        String url = configuration.getEventBusURL();
        String topicName = configuration.getEventBusFindurEventsTopicName();
        
        LOG.info("startUp -> connecting to Findur events topic. url={}, topic={}", url, topicName);

        try {
            eventBusConsumer = new AMQEventBusConsumer(url, DestinationType.TOPIC, topicName);
            eventBusConsumer.connect();
            eventBusConsumer.setListener(this);
            
            LOG.info("startUp <- successfully connected to Findur events topic");
        } catch (Exception e) {
            LOG.error("startUp - failed to connect to event bus", e);
            throw e;
        }
    }

    @Override
    protected void shutDown() throws Exception {
        if (!configuration.isExposureFeatureEnabled()) {
            LOG.info("shutDown - exposure feature is not enabled. Doing nothing");
            return;
        }

        if (!configuration.getEventBusEnabled()) {
            LOG.info("shutDown - event bus is not enabled. Doing nothing");
            return;
        }

        LOG.info("shutDown -> disconnecting from Findur events topic");
        
        if (eventBusConsumer != null) {
            try {
                eventBusConsumer.disconnect();
                LOG.info("shutDown <- successfully disconnected from event bus");
            } catch (Exception e) {
                LOG.error("shutDown - error disconnecting from event bus", e);
                throw e;
            }
        }
    }

    @Override
    public void onMessage(byte[] messageBytes) {
        eventsReceivedCounter.increment();
        
        try {
            String jsonString = new String(messageBytes);
            LOG.debug("onMessage -> received event: {}", jsonString);

            // Deserialize the event
            Event event = jsonMapper.readValue(jsonString, Event.class);

            if (event == null) {
                LOG.warn("onMessage - received null event, skipping");
                return;
            }

            // Process the event
            processEvent(event);
            eventsProcessedCounter.increment();
            
            LOG.debug("onMessage <- successfully processed event: eventId={}", event.getId());

        } catch (Exception e) {
            processingErrorsCounter.increment();
            LOG.error("onMessage - error processing event", e);
        }
    }

    /**
     * Processes a Findur event by notifying all registered listeners
     */
    private void processEvent(Event event) {
        LOG.debug("processEvent -> processing event: id={}, type={}", 
                event.getId(), event.getClass().getSimpleName());

        try {
            List<IFindurEventConsumerListener> currentListeners;
            synchronized (listeners) {
                currentListeners = new ArrayList<>(listeners);
            }

            for (IFindurEventConsumerListener listener : currentListeners) {
                try {
                    listener.onFindurEvent(event);
                } catch (Exception e) {
                    LOG.error("processEvent - error notifying listener: {}", 
                             listener.getClass().getSimpleName(), e);
                    // Continue processing other listeners even if one fails
                }
            }
            
            LOG.debug("processEvent <- notified {} listeners for event: {}", 
                     currentListeners.size(), event.getId());

        } catch (Exception e) {
            LOG.error("processEvent - error processing event: eventId={}", event.getId(), e);
            throw e;
        }
    }

    @Override
    public String serviceName() {
        return "FindurEventConsumer";
    }
}
