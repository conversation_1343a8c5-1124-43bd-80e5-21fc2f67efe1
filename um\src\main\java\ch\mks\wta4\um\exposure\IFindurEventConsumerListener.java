package ch.mks.wta4.um.exposure;

import com.mkspamp.eventbus.model.Event;

/**
 * Interface for listening to Findur events from the FindurEventConsumer.
 * Implementations of this interface can be registered with the FindurEventConsumer
 * to receive notifications when Findur events are received.
 */
public interface IFindurEventConsumerListener {
    
    /**
     * Called when a Findur event is received by the FindurEventConsumer.
     * 
     * @param event The Findur event that was received
     */
    void onFindurEvent(Event event);
}
