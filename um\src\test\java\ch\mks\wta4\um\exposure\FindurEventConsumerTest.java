package ch.mks.wta4.um.exposure;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;

import com.mkspamp.eventbus.model.Event;
import com.mkspamp.eventbus.model.TradeCreatedEvent;
import com.mkspamp.eventbus.model.SpotTrade;

import ch.mks.wta4.configuration.IConfiguration;

/**
 * Unit tests for FindurEventConsumer
 */
public class FindurEventConsumerTest {

    private FindurEventConsumer consumer;
    private IConfiguration configuration;
    private IFindurEventConsumerListener mockListener;

    @Before
    public void setUp() {
        // Mock dependencies
        configuration = mock(IConfiguration.class);
        mockListener = mock(IFindurEventConsumerListener.class);

        // Configure mocks
        when(configuration.isExposureFeatureEnabled()).thenReturn(true);
        when(configuration.getEventBusEnabled()).thenReturn(true);
        when(configuration.getEventBusURL()).thenReturn("tcp://localhost:61616");
        when(configuration.getEventBusFindurEventsTopicName()).thenReturn("findur-events");

        // Create consumer instance
        consumer = new FindurEventConsumer(configuration);
    }

    @Test
    public void testConstructor() {
        assertNotNull("Consumer should be created", consumer);
        assertEquals("Service name should be correct", "FindurEventConsumer", consumer.serviceName());
    }

    @Test
    public void testAddListener() {
        // Test adding a listener
        consumer.addListener(mockListener);
        
        // Verify listener was added by processing an event
        Event testEvent = createTestEvent();
        consumer.onMessage(createEventJson(testEvent).getBytes());
        
        // Verify listener was called
        ArgumentCaptor<Event> eventCaptor = ArgumentCaptor.forClass(Event.class);
        verify(mockListener).onFindurEvent(eventCaptor.capture());
        
        Event capturedEvent = eventCaptor.getValue();
        assertNotNull("Captured event should not be null", capturedEvent);
    }

    @Test
    public void testEventProcessing() {
        // Add listener
        consumer.addListener(mockListener);
        
        // Create test event
        Event testEvent = createTestEvent();
        String eventJson = createEventJson(testEvent);
        
        // Process event
        consumer.onMessage(eventJson.getBytes());
        
        // Verify listener was notified
        verify(mockListener).onFindurEvent(org.mockito.ArgumentMatchers.any(Event.class));
    }

    @Test
    public void testMultipleListeners() {
        // Create multiple listeners
        IFindurEventConsumerListener listener1 = mock(IFindurEventConsumerListener.class);
        IFindurEventConsumerListener listener2 = mock(IFindurEventConsumerListener.class);
        
        // Add listeners
        consumer.addListener(listener1);
        consumer.addListener(listener2);
        
        // Process event
        Event testEvent = createTestEvent();
        consumer.onMessage(createEventJson(testEvent).getBytes());
        
        // Verify both listeners were notified
        verify(listener1).onFindurEvent(org.mockito.ArgumentMatchers.any(Event.class));
        verify(listener2).onFindurEvent(org.mockito.ArgumentMatchers.any(Event.class));
    }

    @Test
    public void testNullEventHandling() {
        // Add listener
        consumer.addListener(mockListener);
        
        // Process null event (empty JSON)
        consumer.onMessage("null".getBytes());
        
        // Verify listener was not called for null event
        verify(mockListener, org.mockito.Mockito.never()).onFindurEvent(org.mockito.ArgumentMatchers.any(Event.class));
    }

    @Test
    public void testInvalidJsonHandling() {
        // Add listener
        consumer.addListener(mockListener);
        
        // Process invalid JSON
        consumer.onMessage("invalid json".getBytes());
        
        // Verify listener was not called for invalid JSON
        verify(mockListener, org.mockito.Mockito.never()).onFindurEvent(org.mockito.ArgumentMatchers.any(Event.class));
    }

    private Event createTestEvent() {
        TradeCreatedEvent event = new TradeCreatedEvent();
        event.setId("test-event-123");
        
        SpotTrade trade = new SpotTrade();
        trade.setId("test-trade-123");
        trade.setSrc("FINDUR");
        trade.setTraderId("trader1");
        trade.setCounterpartyId("counterparty1");
        
        event.setPayload(trade);
        return event;
    }

    private String createEventJson(Event event) {
        // Simple JSON representation for testing
        return "{"
                + "\"id\":\"" + event.getId() + "\","
                + "\"eventType\":\"TradeCreatedEvent\","
                + "\"payload\":{"
                + "\"id\":\"test-trade-123\","
                + "\"src\":\"FINDUR\","
                + "\"traderId\":\"trader1\","
                + "\"counterpartyId\":\"counterparty1\""
                + "}"
                + "}";
    }
}
